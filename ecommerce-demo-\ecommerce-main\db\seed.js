
const { pool } = require('./db');

async function seedDatabase() {
  try {
    // Sample user data
    await pool.query(`
      INSERT INTO users (username, password, email, full_name, phone_number)
      VALUES 
      ('johndo<PERSON>', '$2b$10$encrypted', '<EMAIL>', '<PERSON>', '1234567890'),
      ('janedoe', '$2b$10$encrypted', '<EMAIL>', '<PERSON>', '0987654321')
      ON CONFLICT (username) DO NOTHING;
    `);

    // Sample pickups data
    await pool.query(`
      INSERT INTO pickups (tracking_id, product_name, street_address, city, pincode, pickup_date)
      VALUES 
      ('WTW-123456', 'Old Laptop', '123 Green St', 'Mumbai', '400001', '2024-02-01'),
      ('WTW-789012', 'Broken Phone', '456 Blue Ave', 'Delhi', '110001', '2024-02-02')
      ON CONFLICT (tracking_id) DO NOTHING;
    `);

    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await pool.end();
  }
}

seedDatabase();
