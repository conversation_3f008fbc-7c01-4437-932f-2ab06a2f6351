<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - WasteToWallet</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/signup.css">
    <style>
        /* Additional styles for navigation */
        .main-nav {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            width: 90%;
            max-width: 1200px;
            height: 80px;
            display: flex;
            align-items: center;
            background: #B5F0BD;
            border-radius: 120px;
            transition: all 0.3s ease;
        }
        
        /* Fix position of signup box to account for navbar */
        .container {
            padding-top: 100px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="main-nav">
        <div class="nav-content">
            <div class="nav-left">
                <a href="/">Home</a>
                <a href="/#about">About</a>
                <a href="/services.html">Services</a>
                <a href="/#contact">Contact</a>
                <a href="/tracking.html">Tracking</a>
            </div>
            <div class="nav-right">
                <a href="/login.html" class="login-btn-nav">Login</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="signup-box">
            <h1>Sign Up</h1>
            
            <form id="signupForm">
                <div class="form-group">
                    <input type="text" id="fullName" placeholder="Full name" required>
                </div>
                <div class="form-group">
                    <input type="email" id="email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <input type="password" id="password" placeholder="Password" required>
                </div>
                
                <button type="submit" class="signup-btn">Sign Up</button>
            </form>

            <p class="terms">By signing up you agree to our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></p>

            <div class="or-divider">
                <span class="line"></span>
                <span class="or">OR</span>
                <span class="line"></span>
            </div>

            <div class="social-login">
                <button class="google-btn" id="google-signin-btn">
                    <i class="fab fa-google"></i>Sign up with Google
                </button>
                <button class="facebook-btn">
                    <i class="fab fa-facebook-f"></i>Facebook
                </button>
            </div>

            <div class="login-link">
                <p>Already have an account? 
                    <a href="login.html" class="login-link">Login here</a>
                </p>
            </div>
        </div>
    </div>

    <script src="/js/signup.js"></script>
    <script src="/js/nav.js"></script>
    <script src="/js/auth.js"></script>
    
    <script>
        // Prevent automatic reloads and add session debugging
        window.addEventListener('load', function() {
            console.log('Signup page loaded - preventing automatic reloads');
            
            // Store a session marker in localStorage to detect page reloads
            const sessionMarker = localStorage.getItem('signup_session_marker');
            const newMarker = Date.now().toString();
            
            if (sessionMarker) {
                console.log('Detected page reload, previous session marker:', sessionMarker);
                // Check if we have cookie data
                const hasCookie = document.cookie.includes('ecorecycle.sid');
                console.log('Session cookie present:', hasCookie);
            }
            
            localStorage.setItem('signup_session_marker', newMarker);
            
            // Disable refresh/reload
            function preventReload(e) {
                console.log('Prevented automatic reload attempt');
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
            
            // Listen for beforeunload attempts
            window.addEventListener('beforeunload', function(e) {
                console.log('beforeunload event detected');
                // Don't prevent intentional form submission or navigation
                if (window.intentionalNavigation) return;
                
                // Only prevent automatic reloads, not user navigation
                const navType = performance.getEntriesByType('navigation')[0];
                if (navType && navType.type === 'reload') {
                    return preventReload(e);
                }
            });
            
            // Add form validation with better error handling
            const signupForm = document.getElementById('signupForm');
            
            if (signupForm) {
                const passwordInput = document.getElementById('password');
                
                if (passwordInput) {
                    // Add password strength indicator
                    const strengthIndicator = document.createElement('div');
                    strengthIndicator.className = 'password-strength';
                    strengthIndicator.style.height = '5px';
                    strengthIndicator.style.marginTop = '5px';
                    strengthIndicator.style.backgroundColor = '#ddd';
                    strengthIndicator.style.borderRadius = '2px';
                    
                    const strengthBar = document.createElement('div');
                    strengthBar.style.height = '100%';
                    strengthBar.style.width = '0%';
                    strengthBar.style.backgroundColor = '#ccc';
                    strengthBar.style.borderRadius = '2px';
                    strengthBar.style.transition = 'width 0.3s, background-color 0.3s';
                    
                    strengthIndicator.appendChild(strengthBar);
                    passwordInput.parentNode.appendChild(strengthIndicator);
                    
                    // Check password strength
                    passwordInput.addEventListener('input', function() {
                        const password = this.value;
                        let strength = 0;
                        
                        if (password.length >= 8) strength += 25;
                        if (password.match(/[A-Z]/)) strength += 25;
                        if (password.match(/[0-9]/)) strength += 25;
                        if (password.match(/[^A-Za-z0-9]/)) strength += 25;
                        
                        strengthBar.style.width = strength + '%';
                        
                        if (strength < 25) {
                            strengthBar.style.backgroundColor = '#f44336';
                        } else if (strength < 50) {
                            strengthBar.style.backgroundColor = '#ff9800';
                        } else if (strength < 75) {
                            strengthBar.style.backgroundColor = '#ffeb3b';
                        } else {
                            strengthBar.style.backgroundColor = '#4caf50';
                        }
                    });
                }
                
                // Set intentional navigation flag on form submit
                signupForm.addEventListener('submit', function() {
                    window.intentionalNavigation = true;
                });
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="/js/supabase-client.js"></script>
    <script src="/js/supabase-auth.js"></script>
</body>
</html>
