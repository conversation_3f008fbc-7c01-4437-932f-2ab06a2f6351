// server.js
require('dotenv').config();
const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const session = require('express-session');
const db = require('./db/db');
const multer = require('multer');
const fs = require('fs');

const app = express();
const port = process.env.PORT || 5002;

// Initialize database schema
db.initializeDatabase().then(async () => {
  console.log('Database schema initialized successfully');
  
  // Add login_status column if it doesn't exist
  try {
    await db.query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS is_logged_in BOOLEAN DEFAULT FALSE
    `);
    console.log('Login status tracking column added');
  } catch (err) {
    console.error('Error adding login status column:', err);
  }

  // Add supabase_user_id column if it doesn't exist
  try {
    await db.query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS supabase_user_id UUID UNIQUE
    `);
    console.log('Supabase user ID column added');
  } catch (err) {
    console.error('Error adding supabase user ID column:', err);
  }
}).catch(err => {
  console.error('Failed to initialize database schema:', err);
  console.error('Please make sure your PostgreSQL database is running and properly configured');
});

// Middleware
app.use(express.static(path.join(__dirname, 'public')));

// Serve uploaded files with proper headers
app.use('/uploads', express.static(path.join(__dirname, 'public', 'uploads'), {
  setHeaders: (res, path) => {
    // Set cache headers for uploaded files
    res.setHeader('Cache-Control', 'public, max-age=86400'); // 1 day cache

    // Set proper content type for images
    if (path.endsWith('.jpg') || path.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (path.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (path.endsWith('.gif')) {
      res.setHeader('Content-Type', 'image/gif');
    } else if (path.endsWith('.webp')) {
      res.setHeader('Content-Type', 'image/webp');
    }
  }
}));

app.use(bodyParser.json());

// Session configuration for both local and production environments
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'ecorecycle_secret_key',
  resave: false,
  saveUninitialized: true,
  cookie: { 
    // Never use secure in local dev
    secure: process.env.NODE_ENV === 'production' ? true : false,
    // Long expiration to avoid session loss
    maxAge: 1000 * 60 * 60 * 24 * 30, // 30 days
    httpOnly: true,
    path: '/',
    // Fix cross-site issues in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
  },
  rolling: true, // Reset cookie expiration on every response
  name: 'ecorecycle.sid' // Custom name to avoid conflicts
};

// For Vercel or other production environments, use PostgreSQL to store sessions
if (process.env.NODE_ENV === 'production' || process.env.VERCEL) {
  console.log('Using PostgreSQL session store for production');
  
  // This will store sessions in your existing PostgreSQL database
  const pgSession = require('connect-pg-simple')(session);
  sessionConfig.store = new pgSession({
    pool: db.pool, // Use the same pool as your app
    tableName: 'session', // Table name we created in db.js
    createTableIfMissing: true,
    errorLog: console.error, // Log errors
    pruneSessionInterval: 60 // Clean up expired sessions every minute
  });
  
  // In Vercel, cookie settings need special handling
  if (process.env.VERCEL) {
    console.log('Vercel environment detected, using specialized cookie settings');
    
    // Force secure to be true for SameSite=None
    sessionConfig.cookie.secure = true;
    sessionConfig.cookie.sameSite = 'none';
    
    // Trying to fix Chrome SameSite issues
    sessionConfig.proxy = true; // Trust the reverse proxy
  }
} else {
  console.log('Using default memory session store for development');
}

// Use session middleware
app.use(session(sessionConfig));

// Session check middleware
app.use((req, res, next) => {
  // Log cookie info for debugging
  if (process.env.DEBUG) {
    console.log('Request cookies:', req.headers.cookie);
    console.log('Session ID:', req.session.id);
  }

  // Skip for static files and public endpoints
  const publicPaths = [
    '/api/login', 
    '/api/signup', 
    '/api/track_pickup', 
    '/api/health',
    '/api/session-debug', 
    '/api/last_tracking_id'
  ];
  
  // Skip session check for non-API endpoints and public paths
  const isPublicPath = publicPaths.some(path => req.path.startsWith(path)) || 
                     !req.path.startsWith('/api/');
                     
  if (isPublicPath) {
    return next();
  }

  // Simple but resilient session check
  if (req.session && req.session.userId) {
    // Ensure we always update the session expiry
    req.session.touch();
    return next();
  } else {
    // Handle potential Vercel cold start issues
    if (process.env.VERCEL && !req.headers.cookie) {
      console.log(`Missing cookies in Vercel environment for ${req.path}`);
    } else {
      console.log(`Authentication required for ${req.path}`);
    }
    
    // For API endpoints that require authentication
    return res.status(401).json({ 
      status: 'failed', 
      message: 'Authentication required', 
      requireLogin: true
    });
  }
});

// Configure multer for avatar uploads
// Use memory storage for serverless environments
const avatarStorage = multer.memoryStorage();

// Set up upload middleware with file filtering
const avatarUpload = multer({
  storage: avatarStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
  fileFilter: function(req, file, cb) {
    // Accept only image files
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Google OAuth user creation/login endpoint
app.post('/api/auth/google', async (req, res) => {
  try {
    const { user, session } = req.body;

    if (!user || !user.email) {
      return res.status(400).json({
        status: 'failed',
        message: 'Invalid user data'
      });
    }

    console.log('Google OAuth user received:', user.email);
    console.log('Full user object:', JSON.stringify(user, null, 2));

    // Check if user already exists (by supabase_user_id first, then by email)
    let existingUser = await db.query('SELECT * FROM users WHERE supabase_user_id = $1 OR email = $2', [user.id, user.email]);

    let dbUser;
    if (existingUser.rows.length > 0) {
      // User exists, update their info
      dbUser = existingUser.rows[0];
      console.log('Existing Google user found:', dbUser.id);

      // Update user info if needed - fix the metadata access
      const fullName = user.user_metadata?.full_name || user.user_metadata?.name || user.identities?.[0]?.identity_data?.full_name || dbUser.full_name || user.email.split('@')[0];
      const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture || user.identities?.[0]?.identity_data?.picture || dbUser.avatar_url;

      await db.query(
        'UPDATE users SET full_name = $1, avatar_url = $2, supabase_user_id = $3, updated_at = NOW() WHERE id = $4',
        [fullName, avatarUrl, user.id, dbUser.id]
      );
    } else {
      // Create new user - fix the metadata access
      const fullName = user.user_metadata?.full_name || user.user_metadata?.name || user.identities?.[0]?.identity_data?.full_name || user.email.split('@')[0];
      const avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture || user.identities?.[0]?.identity_data?.picture || null;

      const result = await db.query(
        'INSERT INTO users (username, email, full_name, avatar_url, password, supabase_user_id) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
        [
          user.email,
          user.email,
          fullName,
          avatarUrl,
          'google_oauth', // Placeholder password for OAuth users
          user.id // Store the Supabase user ID
        ]
      );

      dbUser = result.rows[0];
      console.log('New Google user created:', dbUser.id);
    }

    // Create session for the user
    req.session.regenerate(function(err) {
      if (err) {
        console.error('Session regeneration error for Google user:', err);
        return res.status(500).json({
          status: 'failed',
          message: 'Session creation failed'
        });
      }

      // Store user data in session
      req.session.userId = dbUser.id;
      req.session.userEmail = dbUser.email;
      req.session.userName = dbUser.full_name;
      req.session.loggedIn = true;
      req.session.loginTime = new Date().toISOString();
      req.session.authProvider = 'google';

      // Update login status
      db.query('UPDATE users SET is_logged_in = TRUE WHERE id = $1', [dbUser.id])
        .catch(err => console.error('Error updating login status for Google user:', err));

      // Save the session
      req.session.save(function(saveErr) {
        if (saveErr) {
          console.error('Session save error for Google user:', saveErr);
          return res.status(500).json({
            status: 'failed',
            message: 'Session save failed'
          });
        }

        console.log(`Google OAuth session created for user ${dbUser.id}, Session ID: ${req.session.id}`);

        return res.json({
          status: 'success',
          sessionId: req.session.id,
          user: {
            id: dbUser.id,
            email: dbUser.email,
            full_name: dbUser.full_name,
            avatar_url: dbUser.avatar_url
          }
        });
      });
    });
  } catch (error) {
    console.error('Google OAuth error:', error);
    console.error('Error stack:', error.stack);
    return res.status(500).json({
      status: 'failed',
      message: 'Server error during Google authentication',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Authentication Routes
app.post('/api/signup', async (req, res) => {
  try {
    const { fullName, email, password } = req.body;
    
    // Check if email already exists
    const existingUser = await db.query('SELECT * FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      return res.json({ status: 'failed', message: 'Email already exists' });
    }
    
    // Insert new user
    const result = await db.query(
      'INSERT INTO users (username, email, password, full_name) VALUES ($1, $2, $3, $4) RETURNING id, email, full_name',
      [email, email, password, fullName] // In a real app, you'd hash the password
    );
    
    const newUser = result.rows[0];
    
    // Initialize user preferences
    await db.query(
      'INSERT INTO user_notification_preferences (user_id, email_notifications, sms_notifications, pickup_reminders, reward_updates) VALUES ($1, $2, $3, $4, $5)',
      [newUser.id, true, false, true, true]
    );
    
    // Create a new session
    req.session.regenerate(async function(err) {
      if (err) {
        console.error('Session regeneration error during signup:', err);
        return res.json({ 
          status: 'success', 
          user: newUser,
          message: 'Account created but session could not be established. Please log in.'
        });
      }
      
      // Store user data in session
      req.session.userId = newUser.id;
      req.session.userEmail = email;
      req.session.userName = fullName;
      req.session.loggedIn = true;
      req.session.loginTime = new Date().toISOString();
      
      // Update login status
      await db.query('UPDATE users SET is_logged_in = TRUE WHERE id = $1', [newUser.id])
        .catch(err => console.error('Error updating login status after signup:', err));
      
      console.log(`New user signed up and logged in: ${email} (ID: ${newUser.id}), Session ID: ${req.session.id}`);
      
      // Save the session explicitly
      req.session.save(function(err) {
        if (err) {
          console.error('Session save error after signup:', err);
          return res.json({ 
            status: 'success', 
            user: newUser,
            message: 'Account created but session could not be saved. Please log in.'
          });
        }
        
        return res.json({ 
          status: 'success', 
          sessionId: req.session.id,
          user: newUser
        });
      });
    });
  } catch (error) {
    console.error('Signup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Database error during signup. Please check your PostgreSQL connection.' });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    console.log('Login attempt received');
    const { email, password } = req.body;
    
    // Find user by email
    const result = await db.query('SELECT * FROM users WHERE email = $1', [email]);
    
    if (result.rows.length === 0 || result.rows[0].password !== password) {
      return res.json({ status: 'failed', message: 'Invalid credentials' });
    }
    
    const user = result.rows[0];
    console.log(`User ${user.id} (${user.email}) authenticated successfully`);
    
    // Regenerate session to prevent fixation attacks
    req.session.regenerate(function(err) {
      if (err) {
        console.error('Session regeneration error:', err);
        // Continue anyway with the existing session
      }
      
      // Set session data
      req.session.userId = user.id;
      req.session.userEmail = user.email;
      req.session.userName = user.full_name || user.username;
      req.session.isAuthenticated = true;
      req.session.loginTime = new Date().toISOString();
      
      // Save the session explicitly before sending response
      req.session.save(function(saveErr) {
        if (saveErr) {
          console.error('Session save error:', saveErr);
          // Continue anyway
        }
        
        console.log(`Session ${req.session.id} saved for user ${user.id}`);
        
        // Update login status in database
        db.query('UPDATE users SET is_logged_in = TRUE WHERE id = $1', [user.id])
          .catch(dbErr => console.error('Error updating login status:', dbErr));
        
        // Set a supplementary cookie as backup
        // This is used by the frontend as a fallback
        const userAuthCookie = {
          userId: user.id,
          email: user.email,
          name: user.full_name || user.username
        };
        
        res.cookie('user_auth_backup', JSON.stringify(userAuthCookie), {
          maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
          httpOnly: false, // Allow JS to read this cookie
          path: '/',
          // Never secure in development
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax'
        });
        
        // Send response
        return res.json({ 
          status: 'success',
          user: {
            id: user.id,
            email: user.email,
            name: user.full_name || user.username
          }
        });
      });
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error during login' });
  }
});

// Logout endpoint
app.post('/api/logout', async (req, res) => {
  try {
    // Try to get the user ID before destroying the session
    const userId = req.session?.userId;
    
    console.log(`Logout request received for user ID: ${userId}`);
    
    // Update user login status in database if user ID is available
    if (userId) {
      try {
        await db.query('UPDATE users SET is_logged_in = FALSE WHERE id = $1', [userId]);
      } catch (dbErr) {
        console.error('Database error during logout:', dbErr);
        // Continue with logout even if database update fails
      }
    }
    
    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('Error destroying session:', err);
      }
      
      // Clear the session cookie
      res.clearCookie('ecorecycle.sid', { 
        path: '/'
      });
      
      return res.json({ 
        status: 'success',
        message: 'Logged out successfully'
      });
    });
  } catch (error) {
    console.error('Logout error:', error);
    
    // Even if there's an error, still try to clear the cookie
    res.clearCookie('ecorecycle.sid', { path: '/' });
    
    return res.status(500).json({ 
      status: 'success', // Return success even on error to ensure client redirects
      message: 'Logout attempted with errors'
    });
  }
});

// Get current user
app.get('/api/user', async (req, res) => {
  try {
    // Check session for user ID
    if (!req.session.userId) {
      console.log(`[${new Date().toISOString()}] /api/user called with no userId in session`);
      return res.status(401).json({ 
        status: 'failed', 
        message: 'Not authenticated',
        code: 'SESSION_MISSING_USERID'
      });
    }
    
    console.log(`[${new Date().toISOString()}] /api/user called for userId: ${req.session.userId}`);
    
    // Get user data
    const result = await db.query(
      'SELECT id, username, email, full_name, phone_number, address, avatar_url FROM users WHERE id = $1',
      [req.session.userId]
    );
    
    if (result.rows.length === 0) {
      console.log(`[${new Date().toISOString()}] User not found for userId: ${req.session.userId}`);
      return res.status(404).json({ 
        status: 'failed', 
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // Get notification preferences
    const prefResult = await db.query(
      'SELECT * FROM user_notification_preferences WHERE user_id = $1',
      [req.session.userId]
    );
    
    // Get user rewards
    const rewardsPoints = await db.query(
      'SELECT SUM(points) as total_points FROM rewards WHERE user_id = $1 AND is_used = false',
      [req.session.userId]
    );
    
    const user = {
      ...result.rows[0],
      notifications: prefResult.rows[0] || {
        email_notifications: true,
        sms_notifications: false,
        pickup_reminders: true,
        reward_updates: true
      },
      rewards: {
        points: parseInt(rewardsPoints.rows[0]?.total_points || 0),
        co2Saved: 0, // Placeholder for now
        wasteRecycled: 0 // Placeholder for now
      }
    };
    
    console.log(`[${new Date().toISOString()}] User data fetched successfully for userId: ${req.session.userId}`);
    return res.json(user);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error fetching user data:`, error);
    return res.status(500).json({ 
      status: 'failed', 
      message: 'Server error fetching user data',
      code: 'SERVER_ERROR'
    });
  }
});

// Update user profile
app.post('/api/user/update', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const { fullName, phoneNumber, address } = req.body;
    
    await db.query(
      'UPDATE users SET full_name = $1, phone_number = $2, address = $3, updated_at = NOW() WHERE id = $4',
      [fullName, phoneNumber, address, req.session.userId]
    );
    
    return res.json({ status: 'success' });
  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error updating profile' });
  }
});

// Update user password
app.post('/api/user/password', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const { currentPassword, newPassword } = req.body;
    
    // Verify current password
    const result = await db.query('SELECT password FROM users WHERE id = $1', [req.session.userId]);
    
    if (result.rows.length === 0 || result.rows[0].password !== currentPassword) {
      return res.status(400).json({ status: 'failed', message: 'Current password is incorrect' });
    }
    
    // Update password
    await db.query(
      'UPDATE users SET password = $1, updated_at = NOW() WHERE id = $2',
      [newPassword, req.session.userId]
    );
    
    return res.json({ status: 'success' });
  } catch (error) {
    console.error('Update password error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error updating password' });
  }
});

// Update notification settings
app.post('/api/user/notifications', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const { emailNotifications, smsNotifications, pickupReminders, rewardUpdates } = req.body;
    
    // Check if preferences exist
    const checkResult = await db.query(
      'SELECT * FROM user_notification_preferences WHERE user_id = $1',
      [req.session.userId]
    );
    
    if (checkResult.rows.length > 0) {
      // Update existing preferences
      await db.query(
        'UPDATE user_notification_preferences SET email_notifications = $1, sms_notifications = $2, pickup_reminders = $3, reward_updates = $4, updated_at = NOW() WHERE user_id = $5',
        [emailNotifications, smsNotifications, pickupReminders, rewardUpdates, req.session.userId]
      );
    } else {
      // Insert new preferences
      await db.query(
        'INSERT INTO user_notification_preferences (user_id, email_notifications, sms_notifications, pickup_reminders, reward_updates) VALUES ($1, $2, $3, $4, $5)',
        [req.session.userId, emailNotifications, smsNotifications, pickupReminders, rewardUpdates]
      );
    }
    
    return res.json({ status: 'success' });
  } catch (error) {
    console.error('Update notification settings error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error updating notification settings' });
  }
});

// Delete user account
app.post('/api/user/delete', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    // Delete all associated data
    await db.query('DELETE FROM user_notification_preferences WHERE user_id = $1', [req.session.userId]);
    await db.query('DELETE FROM redemptions WHERE user_id = $1', [req.session.userId]);
    await db.query('DELETE FROM rewards WHERE user_id = $1', [req.session.userId]);
    await db.query('DELETE FROM notifications WHERE user_id = $1', [req.session.userId]);
    
    // Update pickups to remove user association but keep the records
    await db.query('UPDATE pickups SET user_id = NULL WHERE user_id = $1', [req.session.userId]);
    
    // Delete the user
    await db.query('DELETE FROM users WHERE id = $1', [req.session.userId]);
    
    // Clear session
    req.session.destroy();
    
    return res.json({ status: 'success' });
  } catch (error) {
    console.error('Delete account error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error deleting account' });
  }
});

// Pickup Routes
app.post('/api/submit_pickup', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    // Validate required fields
    const requiredFields = ['productName', 'streetAddress', 'city', 'pincode'];
    const missingFields = requiredFields.filter(field => !req.body[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({ 
        status: 'failed', 
        message: `Missing required fields: ${missingFields.join(', ')}` 
      });
    }
    
    // Generate a unique tracking ID in WTW-XXXXXX format if not provided
    // Use the client-specified tracking ID if provided
    let trackingId = req.body.trackingId || req.session.forcedTrackingId;
    
    if (!trackingId) {
      const countResult = await db.query('SELECT COUNT(*) FROM pickups');
      const count = parseInt(countResult.rows[0].count) || 0;
      trackingId = `WTW-${(100000 + count + 1).toString().substring(1)}`;
    }
    
    // Generate a unique order ID in ORD-XXXXXX format - always new for each order
    const orderCountResult = await db.query('SELECT COUNT(*) FROM pickups');
    const orderCount = parseInt(orderCountResult.rows[0].count) || 0;
    const orderId = `ORD-${(100000 + orderCount + 1).toString().substring(1)}`;
    
    // Get user details
    const userResult = await db.query(
      'SELECT id, full_name, email, phone_number, address FROM users WHERE id = $1',
      [req.session.userId]
    );
    
    if (userResult.rows.length === 0) {
      return res.status(404).json({ status: 'failed', message: 'User not found' });
    }
    
    const user = userResult.rows[0];
    
    // Prepare date fields
    const pickupDate = req.body.pickupDate ? new Date(req.body.pickupDate) : new Date();
    const purchaseDate = req.body.purchaseDate ? new Date(req.body.purchaseDate) : null;
    
    // Create full address if not provided
    const fullAddress = req.body.fullAddress || 
      `${req.body.streetAddress}, ${req.body.addressLine2 ? req.body.addressLine2 + ', ' : ''}${req.body.city}, ${req.body.pincode}`;
    
    // Insert pickup record
    const result = await db.query(
      `INSERT INTO pickups (
        tracking_id, order_id, user_id, product_name, model, street_address, 
        address_line2, city, pincode, full_address, latitude, 
        longitude, pickup_date, purchase_date, status, current_stage
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16) RETURNING *`,
      [
        trackingId,
        orderId,
        req.session.userId,
        req.body.productName || 'Mixed Recyclables',
        req.body.model || 'Standard',
        req.body.streetAddress || '',
        req.body.addressLine2 || '',
        req.body.city || '',
        req.body.pincode || '',
        fullAddress,
        req.body.latitude || null,
        req.body.longitude || null,
        pickupDate,
        purchaseDate,
        'Scheduled',
        1 // Stage 1: Scheduled
      ]
    );
    
    // Store this tracking ID in session
    if (req.session) {
      req.session.exactTrackingId = trackingId;
      req.session.lastTrackingId = trackingId;
      if (!req.session.forcedTrackingId) {
        req.session.forcedTrackingId = trackingId;
      }
    }
    
    // Add notification for the user
    await db.query(
      'INSERT INTO notifications (user_id, message) VALUES ($1, $2)',
      [req.session.userId, `Your pickup with tracking ID ${trackingId} and order ID ${orderId} has been scheduled.`]
    );
    
    return res.json({
      status: 'success',
      tracking_id: trackingId,
      order_id: orderId,
      pickup: {
        ...result.rows[0],
        full_name: user.full_name,
        email: user.email,
        phone_number: user.phone_number
      }
    });
  } catch (error) {
    console.error('Submit pickup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error submitting pickup request' });
  }
});

// Get user's pickup history
app.get('/api/pickups/user', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const result = await db.query(
      'SELECT * FROM pickups WHERE user_id = $1 ORDER BY created_at DESC',
      [req.session.userId]
    );
    
    return res.json(result.rows);
  } catch (error) {
    console.error('Get user pickups error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching pickup history' });
  }
});

// Get pickup by tracking ID - RESPECT FORCED TRACKING IDs
app.get('/api/pickup/:tracking_id', async (req, res) => {
  try {
    // If there is a LOCKED tracking ID in the session, it always takes precedence
    // unless the URL specifically requests a different one
    let trackingId;
    
    // Determine the tracking ID to use
    if (req.params.tracking_id === 'current') {
      // Use forced ID with highest priority
      if (req.session && req.session.trackingIdLocked && req.session.forcedTrackingId) {
        console.log('Using locked tracking ID from session:', req.session.forcedTrackingId);
        trackingId = req.session.forcedTrackingId;
      }
      // Then try other IDs in order
      else if (req.session && req.session.forcedTrackingId) {
        trackingId = req.session.forcedTrackingId;
      }  
      else if (req.session && req.session.exactTrackingId) {
        trackingId = req.session.exactTrackingId;
      }
      else if (req.session && req.session.lastTrackingId) {
        trackingId = req.session.lastTrackingId;
      }
    } 
    // If explicitly requesting an ID, use that even if there's a locked one
    else {
      trackingId = req.params.tracking_id;
      
      // If we have a different explicit ID, update all stored IDs
      if (req.session) {
        req.session.forcedTrackingId = trackingId;
        req.session.exactTrackingId = trackingId;
        req.session.lastTrackingId = trackingId;
        req.session.trackingIdLocked = true; // Lock this new ID
      }
    }
    
    if (!trackingId) {
      return res.status(400).json({ status: 'failed', message: 'No tracking ID provided' });
    }
    
    // Standardize tracking ID format
    trackingId = trackingId.trim().toUpperCase();
    
    // Join with users table to get customer information
    const result = await db.query(`
      SELECT p.*, u.full_name, u.email, u.phone_number 
      FROM pickups p
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.tracking_id = $1
    `, [trackingId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ 
        status: 'failed', 
        message: 'Pickup not found',
        tracking_id: trackingId  // Return the ID even if pickup not found
      });
    }
    
    return res.json({
      status: 'success',
      pickup: result.rows[0],
      tracking_id: trackingId
    });
  } catch (error) {
    console.error('Get pickup by tracking error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching pickup details' });
  }
});

// Cancel pickup
app.post('/api/pickups/cancel/:id', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const pickupId = req.params.id;
    
    // First check if pickup exists and belongs to user
    const checkResult = await db.query(
      'SELECT * FROM pickups WHERE id = $1 AND user_id = $2',
      [pickupId, req.session.userId]
    );
    
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ status: 'failed', message: 'Pickup not found or not authorized' });
    }
    
    const pickup = checkResult.rows[0];
    
    if (pickup.status !== 'Scheduled' && pickup.status !== 'Pending') {
      return res.status(400).json({ status: 'failed', message: 'Can only cancel scheduled or pending pickups' });
    }
    
    // Update pickup status
    await db.query(
      'UPDATE pickups SET status = $1, updated_at = NOW() WHERE id = $2',
      ['Cancelled', pickupId]
    );
    
    return res.json({ status: 'success' });
  } catch (error) {
    console.error('Cancel pickup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error cancelling pickup' });
  }
});

// Tracking Routes
app.post('/api/track_pickup', async (req, res) => {
  try {
    const { tracking_id } = req.body;
    
    if (!tracking_id) {
      return res.status(400).json({ status: 'failed', message: 'Tracking ID is required' });
    }
    
    // Standardize tracking ID format (uppercase and trim)
    const formattedTrackingId = tracking_id.trim().toUpperCase();
    
    // Verify this is a valid tracking ID format - allow both 6-digit and legacy formats
    const trackingIdPattern = /^WTW-\d+$/;
    if (!trackingIdPattern.test(formattedTrackingId)) {
      return res.status(400).json({ 
        status: 'failed', 
        message: 'Invalid tracking ID format. Expected WTW-XXXXXX format' 
      });
    }
    
    // Store in session (permanent storage until explicitly replaced)
    if (req.session) {
      req.session.lastTrackingId = formattedTrackingId;
      // Set a longer cookie expiry to keep the tracking ID persistent
      req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    }
    
    // Check if this tracking ID exists in database
    const result = await db.query('SELECT * FROM pickups WHERE tracking_id = $1', [formattedTrackingId]);
    
    if (result.rows.length === 0) {
      // Even if not found, still store the ID but return an appropriate response
      return res.json({
        status: 'warning',
        message: 'Tracking ID stored but not found in database',
        tracking_id: formattedTrackingId
      });
    }
    
    return res.json({
      status: 'success',
      message: 'Tracking ID stored successfully',
      tracking_id: formattedTrackingId,
      pickup_data: result.rows[0]
    });
  } catch (error) {
    console.error('Track pickup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error tracking pickup' });
  }
});

// New endpoint to force a specific tracking ID as the current one
app.get('/api/set_tracking_id/:tracking_id', (req, res) => {
  try {
    const trackingId = req.params.tracking_id.trim().toUpperCase();
    
    // Skip validation and force this ID to be the current one
    if (req.session) {
      // Always prioritize this explicitly selected ID
      req.session.exactTrackingId = trackingId;
      req.session.lastTrackingId = trackingId;
      req.session.forcedTrackingId = trackingId; // Special flag to indicate user explicit selection
      
      // Mark this as a FORCED ID that should never be overridden
      req.session.trackingIdLocked = true;
      
      // Set a longer cookie expiry to keep the tracking ID persistent
      req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    }
    
    return res.json({
      status: 'success',
      message: 'Tracking ID set successfully',
      tracking_id: trackingId
    });
  } catch (error) {
    console.error('Set tracking ID error:', error);
    return res.status(500).json({ 
      status: 'failed', 
      message: 'Server error setting tracking ID'
    });
  }
});

// Get the most recently used tracking ID with improved priorities
app.get('/api/last_tracking_id', (req, res) => {
  // First try to get the forced tracking ID (explicit user selection) if it exists
  if (req.session && req.session.forcedTrackingId && req.session.trackingIdLocked) {
    console.log('Returning LOCKED tracking ID:', req.session.forcedTrackingId);
    return res.json({
      status: 'success',
      tracking_id: req.session.forcedTrackingId,
      source: 'forced_selection',
      locked: true
    });
  }
  
  // First try to get the forced tracking ID (explicit user selection) if it exists
  if (req.session && req.session.forcedTrackingId) {
    return res.json({
      status: 'success',
      tracking_id: req.session.forcedTrackingId,
      source: 'forced_selection'
    });
  }
  
  // Next try the exact tracking ID
  if (req.session && req.session.exactTrackingId) {
    return res.json({
      status: 'success',
      tracking_id: req.session.exactTrackingId,
      source: 'exact_id'
    });
  }
  
  // Fall back to last tracking ID
  if (req.session && req.session.lastTrackingId) {
    return res.json({
      status: 'success',
      tracking_id: req.session.lastTrackingId,
      source: 'session'
    });
  }
  
  // If no session ID found, send appropriate response
  return res.json({
    status: 'failed',
    message: 'No recent tracking ID found'
  });
});

// Get user rewards
app.get('/api/rewards/user', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    // Calculate total rewards points
    const pointsResult = await db.query(
      'SELECT SUM(points) as total_points FROM rewards WHERE user_id = $1 AND is_used = false',
      [req.session.userId]
    );
    
    // Get total waste recycled (from completed pickups)
    const pickupsResult = await db.query(
      'SELECT COUNT(*) as total_pickups FROM pickups WHERE user_id = $1 AND status = $2',
      [req.session.userId, 'Completed']
    );
    
    // Calculate estimated CO2 saved (simplified calculation)
    const totalPoints = parseInt(pointsResult.rows[0]?.total_points || 0);
    const totalPickups = parseInt(pickupsResult.rows[0]?.total_pickups || 0);
    
    // Simplified calculation: 1 pickup ≈ 2kg of waste, 1kg of recycled waste ≈ 2.5kg CO2 saved
    const wasteRecycled = totalPickups * 2; // kg
    const co2Saved = wasteRecycled * 2.5; // kg
    
    return res.json({
      points: totalPoints,
      co2Saved,
      wasteRecycled
    });
  } catch (error) {
    console.error('Get user rewards error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching rewards data' });
  }
});

// Redeem reward
app.post('/api/rewards/redeem', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const { points, rewardName } = req.body;
    
    if (!points || !rewardName) {
      return res.status(400).json({ status: 'failed', message: 'Points and reward name are required' });
    }
    
    // Check if user has enough points
    const pointsResult = await db.query(
      'SELECT SUM(points) as total_points FROM rewards WHERE user_id = $1 AND is_used = false',
      [req.session.userId]
    );
    
    const totalPoints = parseInt(pointsResult.rows[0]?.total_points || 0);
    
    if (totalPoints < points) {
      return res.status(400).json({ status: 'failed', message: 'Not enough points' });
    }
    
    // Start a transaction to ensure both operations complete
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // Record the redemption
      await client.query(
        'INSERT INTO redemptions (user_id, reward_name, points_used) VALUES ($1, $2, $3)',
        [req.session.userId, rewardName, points]
      );
      
      // Mark points as used, starting with oldest rewards first
      await client.query(`
        WITH points_to_use AS (
          SELECT id
          FROM rewards
          WHERE user_id = $1 AND is_used = false
          ORDER BY created_at ASC
          LIMIT (
            SELECT COUNT(*)
            FROM rewards
            WHERE user_id = $1 AND is_used = false
            ORDER BY created_at ASC
            LIMIT (
              SELECT LEAST(
                COUNT(*),
                CEIL($2::float / AVG(points)::float)
              )
              FROM rewards
              WHERE user_id = $1 AND is_used = false
            )
          )
        )
        UPDATE rewards
        SET is_used = true
        WHERE id IN (SELECT id FROM points_to_use)
      `, [req.session.userId, points]);
      
      await client.query('COMMIT');
      
      return res.json({ status: 'success' });
    } catch (err) {
      await client.query('ROLLBACK');
      throw err;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Redeem rewards error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error redeeming rewards' });
  }
});

// Location API
app.post('/api/get_address', async (req, res) => {
  const { latitude, longitude } = req.body;
  
  try {
    const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`, {
      headers: { 'User-Agent': 'EcoRecycle App' }
    });
    
    if (response.ok) {
      const data = await response.json();
      res.json(data);
    } else {
      res.status(400).json({ error: 'Failed to fetch address' });
    }
  } catch (error) {
    console.error('Error fetching address:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// User avatar upload endpoint with error handling
app.post('/api/user/avatar', (req, res) => {
  // Handle multer errors
  avatarUpload.single('avatar')(req, res, async (err) => {
    if (err) {
      console.error('Multer error:', err);

      // Handle specific multer errors
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          status: 'failed',
          message: 'File too large. Maximum size is 5MB.',
          code: 'FILE_TOO_LARGE'
        });
      } else if (err.message === 'Only image files are allowed!') {
        return res.status(400).json({
          status: 'failed',
          message: 'Only image files are allowed (JPEG, PNG, GIF, WebP).',
          code: 'INVALID_FILE_TYPE'
        });
      } else {
        return res.status(400).json({
          status: 'failed',
          message: err.message || 'File upload error',
          code: 'UPLOAD_ERROR'
        });
      }
    }

    // Continue with the upload logic
    console.log(`[${new Date().toISOString()}] Avatar upload request received`);
    console.log('Session info:', {
      sessionId: req.session?.id,
      userId: req.session?.userId,
      userEmail: req.session?.userEmail
    });
    console.log('File info:', req.file ? {
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype,
      bufferLength: req.file.buffer?.length
    } : 'No file');

    try {
      // Check authentication
      if (!req.session || !req.session.userId) {
        console.error('Avatar upload failed: No valid session or userId');
        return res.status(401).json({
          status: 'failed',
          message: 'Not authenticated',
          code: 'NO_SESSION'
        });
      }

      if (!req.file || !req.file.buffer) {
        console.error('Avatar upload failed: No file uploaded or file buffer missing');
        return res.status(400).json({
          status: 'failed',
          message: 'No file uploaded',
          code: 'NO_FILE'
        });
      }

      // Verify user exists in database
      const userCheck = await db.query('SELECT id FROM users WHERE id = $1', [req.session.userId]);
      if (userCheck.rows.length === 0) {
        console.error(`Avatar upload failed: User ${req.session.userId} not found in database`);
        return res.status(404).json({
          status: 'failed',
          message: 'User not found',
          code: 'USER_NOT_FOUND'
        });
      }

      // Convert file buffer to base64 data URL
      const base64Data = req.file.buffer.toString('base64');
      const avatarDataUrl = `data:${req.file.mimetype};base64,${base64Data}`;

      console.log(`Generated avatar data URL (first 100 chars): ${avatarDataUrl.substring(0, 100)}...`);

      // Update user's avatar URL in database with the data URL
      const updateResult = await db.query(
        'UPDATE users SET avatar_url = $1, updated_at = NOW() WHERE id = $2 RETURNING avatar_url',
        [avatarDataUrl, req.session.userId]
      );

      if (updateResult.rows.length === 0) {
        console.error(`Avatar upload failed: Could not update user ${req.session.userId} in database`);
        return res.status(500).json({
          status: 'failed',
          message: 'Failed to update user avatar',
          code: 'DB_UPDATE_FAILED'
        });
      }

      console.log(`Avatar uploaded successfully for user ${req.session.userId}`);

      return res.json({
        status: 'success',
        message: 'Avatar uploaded successfully',
        avatar: avatarDataUrl,
        userId: req.session.userId
      });
    } catch (error) {
      console.error('Avatar upload error:', error);

      return res.status(500).json({
        status: 'failed',
        message: 'Server error uploading avatar',
        code: 'SERVER_ERROR',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });
});

// Avatar file check endpoint
app.get('/api/user/avatar/check', async (req, res) => {
  try {
    if (!req.session || !req.session.userId) {
      return res.status(401).json({
        status: 'failed',
        message: 'Not authenticated'
      });
    }

    // Get user's current avatar URL from database
    const result = await db.query(
      'SELECT avatar_url FROM users WHERE id = $1',
      [req.session.userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        status: 'failed',
        message: 'User not found'
      });
    }

    const avatarUrl = result.rows[0].avatar_url;

    if (!avatarUrl) {
      return res.json({
        status: 'success',
        hasAvatar: false,
        avatar: null
      });
    }

    // Check if it's a data URL (base64 encoded image)
    const isDataUrl = avatarUrl.startsWith('data:');

    return res.json({
      status: 'success',
      hasAvatar: true,
      avatar: avatarUrl,
      isDataUrl: isDataUrl,
      size: isDataUrl ? avatarUrl.length : undefined
    });
  } catch (error) {
    console.error('Avatar check error:', error);
    return res.status(500).json({
      status: 'failed',
      message: 'Server error checking avatar'
    });
  }
});

// Serve HTML pages
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

app.get('/signup', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'signup.html'));
});

app.get('/services', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'services.html'));
});

app.get('/pickup-details', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'pickup-details.html'));
});

app.get('/pickup-confirmation', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'pickup-confirmation.html'));
});

app.get('/tracking', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'tracking.html'));
});

app.get('/partnership', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'partnership.html'));
});

app.get('/team', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'team.html'));
});

app.get('/start_recycling', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'start_recycling.html'));
});

app.get('/profile', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'profile.html'));
});

// Admin API endpoints
app.get('/api/admin/pickups', async (req, res) => {
  try {
    if (!req.session.userId || !req.session.isAdmin) {
      return res.status(401).json({ status: 'failed', message: 'Not authorized' });
    }
    
    const query = `
      SELECT p.*, u.full_name, u.email, u.phone_number
      FROM pickups p
      JOIN users u ON p.user_id = u.id
      ORDER BY p.pickup_date DESC
    `;
    
    const result = await db.query(query);
    
    return res.json({
      status: 'success',
      pickups: result.rows.map(pickup => ({
        ...pickup,
        tracking_id: pickup.tracking_id,
        order_id: pickup.order_id || `ORD-${pickup.tracking_id.substring(4)}`, // Fallback for legacy records
        pickup_date: formatDateForDisplay(pickup.pickup_date),
        purchase_date: pickup.purchase_date ? formatDateForDisplay(pickup.purchase_date) : null
      }))
    });
  } catch (error) {
    console.error('Admin get pickups error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error retrieving pickups' });
  }
});

// Middleware to ensure user is an admin
function ensureAdmin(req, res, next) {
  if (!req.session || !req.session.user || req.session.user.role !== 'admin') {
    return res.status(403).json({ error: 'Unauthorized' });
  }
  next();
}

// Get pickup by ID
app.get('/api/pickups/:id', async (req, res) => {
  try {
    const pickupId = req.params.id;
    
    // Check if id is a number or tracking ID
    let query, params;
    if (/^\d+$/.test(pickupId)) {
      query = 'SELECT * FROM pickups WHERE id = $1';
      params = [parseInt(pickupId)];
    } else {
      query = 'SELECT * FROM pickups WHERE tracking_id = $1';
      params = [pickupId];
    }
    
    const result = await db.query(query, params);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ status: 'failed', message: 'Pickup not found' });
    }
    
    const pickup = result.rows[0];
    
    // Check authorization if user is logged in
    if (req.session.userId && pickup.user_id && pickup.user_id !== req.session.userId) {
      return res.status(403).json({ status: 'failed', message: 'Not authorized to view this pickup' });
    }
    
    return res.json(pickup);
  } catch (error) {
    console.error('Get pickup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching pickup details' });
  }
});

// Get all pickups for a user
app.get('/api/pickups', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ status: 'failed', message: 'Not authenticated' });
    }
    
    const query = `
      SELECT p.*, u.full_name, u.email, u.phone_number
      FROM pickups p
      JOIN users u ON p.user_id = u.id
      WHERE p.user_id = $1
      ORDER BY p.pickup_date DESC
    `;
    
    const result = await db.query(query, [req.session.userId]);
    
    return res.json({
      status: 'success',
      pickups: result.rows.map(pickup => ({
        ...pickup,
        tracking_id: pickup.tracking_id,
        order_id: pickup.order_id || `ORD-${pickup.tracking_id.substring(4)}`, // Fallback for legacy records
        pickup_date: formatDateForDisplay(pickup.pickup_date),
        purchase_date: pickup.purchase_date ? formatDateForDisplay(pickup.purchase_date) : null
      }))
    });
  } catch (error) {
    console.error('Get pickups error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error retrieving pickups' });
  }
});

// Get pickup by tracking ID
app.get('/api/pickup/:id', async (req, res) => {
  try {
    const trackingId = req.params.id;
    
    // First try to find by tracking ID
    let query = `
      SELECT p.*, u.full_name, u.email, u.phone_number
      FROM pickups p
      JOIN users u ON p.user_id = u.id
      WHERE p.tracking_id = $1
    `;
    
    let result = await db.query(query, [trackingId]);
    
    // If not found by tracking ID, try finding by order ID
    if (result.rows.length === 0) {
      query = `
        SELECT p.*, u.full_name, u.email, u.phone_number
        FROM pickups p
        JOIN users u ON p.user_id = u.id
        WHERE p.order_id = $1
      `;
      
      result = await db.query(query, [trackingId]);
    }
    
    if (result.rows.length === 0) {
      return res.status(404).json({ status: 'failed', message: 'Pickup not found' });
    }
    
    const pickup = result.rows[0];
    
    return res.json({
      status: 'success',
      pickup: {
        ...pickup,
        tracking_id: pickup.tracking_id,
        order_id: pickup.order_id || `ORD-${pickup.tracking_id.substring(4)}`, // Fallback for legacy records
        pickup_date: formatDateForDisplay(pickup.pickup_date),
        purchase_date: pickup.purchase_date ? formatDateForDisplay(pickup.purchase_date) : null
      }
    });
  } catch (error) {
    console.error('Get pickup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error retrieving pickup' });
  }
});

// Admin API to get single pickup details
app.get('/api/admin/pickup/:id', async (req, res) => {
  try {
    if (!req.session.userId || !req.session.isAdmin) {
      return res.status(401).json({ status: 'failed', message: 'Not authorized' });
    }
    
    const { id } = req.params;
    
    const query = `
      SELECT p.*, u.full_name, u.email, u.phone_number, u.street_address, u.city, u.state, u.pincode
      FROM pickups p
      JOIN users u ON p.user_id = u.id
      WHERE p.tracking_id = $1
    `;
    
    const result = await db.query(query, [id]);
    
    if (result.rows.length === 0) {
      // If not found by tracking ID, try to find it by order ID
      const orderQuery = `
        SELECT p.*, u.full_name, u.email, u.phone_number, u.street_address, u.city, u.state, u.pincode
        FROM pickups p
        JOIN users u ON p.user_id = u.id
        WHERE p.order_id = $1
      `;
      
      const orderResult = await db.query(orderQuery, [id]);
      
      if (orderResult.rows.length === 0) {
        return res.status(404).json({ status: 'failed', message: 'Pickup not found' });
      }
      
      const pickup = orderResult.rows[0];
      return res.json({
        status: 'success',
        pickup: {
          ...pickup,
          tracking_id: pickup.tracking_id,
          order_id: pickup.order_id,
          pickup_date: formatDateForDisplay(pickup.pickup_date),
          purchase_date: pickup.purchase_date ? formatDateForDisplay(pickup.purchase_date) : null
        }
      });
    }
    
    const pickup = result.rows[0];
    return res.json({
      status: 'success',
      pickup: {
        ...pickup,
        tracking_id: pickup.tracking_id,
        order_id: pickup.order_id || `ORD-${pickup.tracking_id.substring(4)}`, // Fallback for legacy records
        pickup_date: formatDateForDisplay(pickup.pickup_date),
        purchase_date: pickup.purchase_date ? formatDateForDisplay(pickup.purchase_date) : null
      }
    });
  } catch (error) {
    console.error('Admin get pickup error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error retrieving pickup' });
  }
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    // Test database connection
    const result = await db.query('SELECT NOW()');
    return res.json({
      status: 'ok',
      message: 'Server is running',
      database: 'connected',
      database_time: result.rows[0].now,
      environment: process.env.NODE_ENV,
      database_type: 'Supabase PostgreSQL',
      version: '1.0.0'
    });
  } catch (error) {
    console.error('Health check error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Session debug endpoint
app.get('/api/session-debug', (req, res) => {
  // Generate a comprehensive session report
  const sessionInfo = {
    exists: !!req.session,
    id: req.session?.id,
    cookie: req.session?.cookie ? {
      maxAge: req.session.cookie.maxAge,
      expires: req.session.cookie.expires ? req.session.cookie.expires.toISOString() : null,
      secure: req.session.cookie.secure,
      httpOnly: req.session.cookie.httpOnly
    } : null,
    userId: req.session?.userId,
    userEmail: req.session?.userEmail,
    userName: req.session?.userName,
    loggedIn: req.session?.loggedIn,
    loginTime: req.session?.loginTime,
    clientIP: req.ip,
    userAgent: req.headers['user-agent'],
    requestTime: new Date().toISOString()
  };
  
  // Log the debugging info
  console.log(`[${new Date().toISOString()}] Session debug for IP ${req.ip}:`, sessionInfo);
  
  // Check if client sent the session cookie
  const hasCookie = !!req.headers.cookie;
  console.log(`Client sent cookie header: ${hasCookie ? 'Yes' : 'No'}`);
  if (hasCookie) {
    console.log('Cookie header:', req.headers.cookie);
  }
  
  return res.json({
    ...sessionInfo,
    hasCookieHeader: hasCookie
  });
});

// Test endpoint to verify JSON responses work
app.get('/api/test', (req, res) => {
  res.json({
    status: 'success',
    message: 'Server is working correctly',
    timestamp: new Date().toISOString()
  });
});

// Error handling for 404
app.use((req, res) => {
  res.status(404).sendFile(path.join(__dirname, 'public', '404.html'));
});

// Start server
app.listen(port, '0.0.0.0', () => {
  console.log(`Server running on http://localhost:${port}`);
});