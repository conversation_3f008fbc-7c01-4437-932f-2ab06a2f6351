const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Setting NODE_TLS_REJECT_UNAUTHORIZED to work around SSL issues
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// Get database connection string from environment variables
const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;
if (!connectionString) {
  console.error('Error: POSTGRES_URL or DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Create a new pool with the Supabase connection
const pool = new Pool({
  connectionString,
  ssl: {
    rejectUnauthorized: false
  }
});

async function deploySchema() {
  console.log('Deploying schema to Supabase database...');
  
  const client = await pool.connect();
  try {
    // Read the schema file
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Split schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement in a transaction
    await client.query('BEGIN');
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        await client.query(statement + ';');
        console.log(`Executed statement ${i + 1}/${statements.length} successfully`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}:`, error.message);
        console.error('Statement:', statement);
        throw error;
      }
    }
    
    await client.query('COMMIT');
    console.log('Schema deployment completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error deploying schema:', error);
    process.exit(1);
  } finally {
    client.release();
    pool.end();
  }
}

// Run the deployment
deploySchema().catch(err => {
  console.error('Unhandled error during schema deployment:', err);
  process.exit(1);
}); 