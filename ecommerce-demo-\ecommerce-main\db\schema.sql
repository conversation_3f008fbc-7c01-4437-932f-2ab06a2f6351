-- Create tables first
CREATE TABLE IF NOT EXISTS "session" (
  "sid" varchar NOT NULL COLLATE "default",
  "sess" json NOT NULL,
  "expire" timestamp(6) NOT NULL,
  CONSTRAINT "session_pkey" PRIMARY KEY ("sid")
);

CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON "session" ("expire");

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    phone_number VARCHAR(20),
    address TEXT,
    avatar_url TEXT,
    is_logged_in BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS pickups (
    id SERIAL PRIMARY KEY,
    tracking_id VARCHAR(20) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id),
    product_name VARCHAR(255) NOT NULL,
    model VARCHAR(255),
    street_address TEXT NOT NULL,
    address_line2 TEXT,
    city VARCHAR(100) NOT NULL,
    pincode VARCHAR(20) NOT NULL,
    full_address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    pickup_date DATE NOT NULL,
    purchase_date DATE,
    status VARCHAR(50) DEFAULT 'Scheduled',
    current_stage INTEGER DEFAULT 0,
    rewards_points INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add order_id column if it doesn't exist
ALTER TABLE pickups ADD COLUMN IF NOT EXISTS order_id VARCHAR(30) UNIQUE;

CREATE TABLE IF NOT EXISTS rewards (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    points INTEGER NOT NULL,
    description TEXT,
    expiry_date DATE,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS redemptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    reward_name VARCHAR(255) NOT NULL,
    points_used INTEGER NOT NULL,
    redemption_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    pickup_reminders BOOLEAN DEFAULT TRUE,
    reward_updates BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create view for pickup details
DROP VIEW IF EXISTS pickup_customer_details;
CREATE VIEW pickup_customer_details AS
SELECT 
    p.id AS pickup_id,
    p.tracking_id,
    p.order_id,
    p.product_name,
    p.model,
    p.street_address,
    p.address_line2,
    p.city,
    p.pincode,
    p.pickup_date,
    p.status,
    p.rewards_points,
    u.full_name AS customer_name,
    u.email AS customer_email,
    u.phone_number AS customer_phone
FROM pickups p
JOIN users u ON p.user_id = u.id;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_pickups_tracking_id ON pickups(tracking_id);
CREATE INDEX IF NOT EXISTS idx_pickups_order_id ON pickups(order_id);
CREATE INDEX IF NOT EXISTS idx_pickups_user_id ON pickups(user_id);

-- Add login status tracking column if it doesn't exist (migration)
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_logged_in BOOLEAN DEFAULT FALSE;