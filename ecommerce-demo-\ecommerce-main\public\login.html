<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page - WasteToWallet</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="css/login.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        /* Additional styles for navigation */
        .main-nav {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            width: 90%;
            max-width: 1200px;
            height: 80px;
            display: flex;
            align-items: center;
            background: #B5F0BD;
            border-radius: 120px;
            transition: all 0.3s ease;
        }
        
        /* Fix position of login box to account for navbar */
        .container {
            padding-top: 100px;
        }
        
        /* Style for active link in navbar */
        .active-link {
            background: #45a049 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="main-nav">
        <div class="nav-content">
            <div class="nav-left">
                <a href="/">Home</a>
                <a href="/#about">About</a>
                <a href="/services.html">Services</a>
                <a href="/#contact">Contact</a>
                <a href="/tracking.html">Tracking</a>
            </div>
            <div class="nav-right">
                <a href="/login.html" class="login-btn-nav active-link">Login</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="login-box">
            <h1>Log In</h1>
            
            <form id="login-form">
                <div class="form-group">
                    <input type="email" id="email" placeholder="Email Address" required>
                </div>
                <div class="form-group">
                    <input type="password" id="password" placeholder="Password" required>
                </div>
                
                <button type="submit" class="login-btn">Log In</button>
            </form>

            <div class="or-divider">
                <div class="line"></div>
                <span class="or">OR</span>
                <div class="line"></div>
            </div>

            <div class="social-login">
                <button class="google-btn" id="google-signin-btn">
                    <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" class="social-icon">
                    Continue with Google
                </button>
                <button class="facebook-btn">
                    <img src="/images/facebook_logo.png" alt="Facebook" class="social-icon">
                    Continue with Facebook
                </button>
            </div>

            <div class="login-links">
                <a href="#" class="forgot-password">Forgot Password?</a>
                <p class="signup-link">Don't have an account? 
                    <a href="signup.html" class="signup-link">Sign Up</a>
                </p>
            </div>
        </div>
    </div>
    <script src="js/login.js"></script>
    <script src="/js/nav.js"></script>
    <script src="/js/auth.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="/js/supabase-client.js"></script>
    <script src="/js/supabase-auth.js"></script>
</body>
</html>
