const { Pool } = require('pg');
require('dotenv').config();

// Disable certificate validation globally
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

console.log('WARNING: SSL certificate validation is disabled for database connections');

// Function to create a database configuration
function createDbConfig() {
  // Use direct URL without SSL parameters for better compatibility
  const connectionString = process.env.POSTGRES_DIRECT_URL || process.env.DATABASE_URL;
  
  if (!connectionString) {
    console.error('ERROR: No database connection string found in environment variables');
    process.exit(1);
  }
  
  // Log the connection string with password masked
  console.log('Using connection string:', connectionString.replace(/:[^:]*@/, ':*****@'));
  
  return {
    connectionString,
    ssl: {
      rejectUnauthorized: false
    },
    // Add connection optimization settings
    connectionTimeoutMillis: 10000, // 10 seconds
    idleTimeoutMillis: 30000, // 30 seconds
    max: 10, // Maximum number of clients in the pool
    statement_timeout: 60000 // 60 second query timeout (increased from 30s)
  };
}

// Configure database connection for Supabase
const dbConfig = createDbConfig();

console.log('Using Supabase database configuration');

// Create a connection pool
const pool = new Pool(dbConfig);

// Set statement timeout on all connections
pool.on('connect', (client) => {
  client.query('SET statement_timeout TO 90000'); // 90 seconds timeout
  console.log('Set statement_timeout for new database connection');
});

// Test the connection and initialize schema
let schemaInitialized = false;
let initializationInProgress = false;
let initializationError = null;

async function initializeDatabase() {
  if (initializationInProgress) {
    // Wait for existing initialization to complete
    while (initializationInProgress) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    if (initializationError) {
      throw initializationError;
    }
    return;
  }

  if (schemaInitialized) {
    return;
  }

  initializationInProgress = true;

  try {
    // Test connection first
    const testResult = await pool.query('SELECT NOW()');
    console.log('Database connected successfully at:', testResult.rows[0].now);

    try {
      // Check if schema already exists by checking for tables
      const tablesCheck = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
          AND table_name = 'users'
      `);

      if (tablesCheck.rows.length > 0) {
        console.log('Database schema already exists, skipping schema initialization');
        schemaInitialized = true;
        return;
      }
    } catch (error) {
      console.warn('Could not check existing schema, will attempt to initialize:', error.message);
    }

    // Read and execute schema file
    const fs = require('fs');
    const path = require('path');
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute schema as a transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Split schema into individual statements
      const statements = schema
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      // Execute each statement sequentially
      for (const statement of statements) {
        try {
          await client.query(statement + ';');
        } catch (statementError) {
          console.warn(`Error executing statement: ${statementError.message}`);
          console.warn('Statement was:', statement);
          // Continue with next statement instead of failing completely
        }
      }

      await client.query('COMMIT');
      schemaInitialized = true;
      console.log('Database schema initialized successfully');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error initializing database:', error);
    initializationError = error;
    throw error;
  } finally {
    initializationInProgress = false;
  }
}

// Wrapper for query execution that ensures schema is initialized
async function query(text, params) {
  if (!schemaInitialized) {
    await initializeDatabase();
  }
  return pool.query(text, params);
}

// Create a client method to get a client from the pool
const getClient = async () => {
  const client = await pool.connect();
  return client;
};

// Export the pool for use with session store
module.exports = {
  pool,
  initializeDatabase: async () => {
    // First call the existing initialization
    await initializeDatabase();
    
    // Then ensure session table exists
    try {
      // Create session table for connect-pg-simple
      await pool.query(`
        CREATE TABLE IF NOT EXISTS "session" (
          "sid" varchar NOT NULL COLLATE "default",
          "sess" json NOT NULL,
          "expire" timestamp(6) NOT NULL,
          CONSTRAINT "session_pkey" PRIMARY KEY ("sid")
        );
        CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON "session" ("expire");
      `);
      
      console.log('Session table initialized successfully');
    } catch (error) {
      console.error('Error initializing session table:', error);
    }
    
    return;
  },
  query,
  getClient
};