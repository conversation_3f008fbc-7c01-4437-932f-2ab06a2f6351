document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, setting up Google sign-in');
  
  // Set up Google sign-in button
  const googleBtn = document.getElementById('google-signin-btn');
  console.log('Google button found:', !!googleBtn);
  
  if (googleBtn) {
    googleBtn.addEventListener('click', function(e) {
      e.preventDefault();
      console.log('Google sign-in button clicked');
      signInWithGoogle();
    });
  }
  
  // Sign in with Google
  async function signInWithGoogle() {
    try {
      console.log('Attempting to sign in with Google...');
      console.log('Supabase client:', supabase);
      console.log('Redirect URL:', window.location.origin + '/auth-callback.html');

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin + '/auth-callback.html'
        }
      });

      if (error) {
        console.error('Supabase OAuth error:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        alert(`Error signing in with Google: ${error.message}`);
        throw error;
      }

      console.log('Sign in response:', data);
      console.log('Sign in successful, redirecting to Google');
      // User will be redirected to Google for authentication
    } catch (error) {
      console.error('Error signing in with Google:', error);
      console.error('Error stack:', error.stack);
      alert(`Error signing in with Google: ${error.message || 'Unknown error'}`);
    }
  }
});
