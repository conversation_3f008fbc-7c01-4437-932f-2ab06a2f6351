document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, setting up Google sign-in');
  
  // Set up Google sign-in button
  const googleBtn = document.getElementById('google-signin-btn');
  console.log('Google button found:', !!googleBtn);
  
  if (googleBtn) {
    googleBtn.addEventListener('click', function(e) {
      e.preventDefault();
      console.log('Google sign-in button clicked');
      signInWithGoogle();
    });
  }
  
  // Sign in with Google
  async function signInWithGoogle() {
    try {
      console.log('Attempting to sign in with Google...');
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin + '/auth-callback.html',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        }
      });
      
      if (error) {
        console.error('Supabase OAuth error:', error);
        throw error;
      }
      
      console.log('Sign in successful, redirecting to Google');
    } catch (error) {
      console.error('Error signing in with Google:', error);
      alert('Error signing in with Google. Please try again.');
    }
  }
});

